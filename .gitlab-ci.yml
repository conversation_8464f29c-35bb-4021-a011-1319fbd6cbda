stages:
  - build
  - deploy

# 生产环境构建
build_production:
  stage: build
  tags:
    - shell
  script:
    - docker rmi ************:9092/ai_server:latest || true
    - docker build -t ************:9092/ai_server:latest .
    - docker push ************:9092/ai_server:latest
  only:
    - main

# 测试环境构建
build_test:
  stage: build
  tags:
    - shell
  script:
    - docker rmi ************:9092/ai_server:test || true
    - docker build -t ************:9092/ai_server:test .
    - docker push ************:9092/ai_server:test
  only:
    - develop

# 生产环境部署
deploy_production:
  stage: deploy
  tags:
    - shell
  before_script:
    - eval "$(ssh-agent -s)"
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan $SSH_HOST >> ~/.ssh/known_hosts
  script:
    - ssh -v $SSH_USER@$SSH_HOST
    - ssh $SSH_USER@$SSH_HOST "docker pull ************:9092/ai_server:latest"
    - scp scripts/restart.sh $SSH_USER@$SSH_HOST:/tmp/
    - ssh $SSH_USER@$SSH_HOST "chmod +x /tmp/restart.sh"
    - ssh $SSH_USER@$SSH_HOST "/tmp/restart.sh"
  environment:
    name: production
  only:
    - main

# 测试环境部署
deploy_test:
  stage: deploy
  tags:
    - shell
  before_script:
    - eval "$(ssh-agent -s)"
    - echo "$SSH_PRIVATE_KEY_TEST" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan $SSH_HOST_TEST >> ~/.ssh/known_hosts
  script:
    - ssh -v $SSH_USER_TEST@$SSH_HOST_TEST
    - ssh $SSH_USER_TEST@$SSH_HOST_TEST "docker pull ************:9092/ai_server:test"
    - scp scripts/restart_test.sh $SSH_USER_TEST@$SSH_HOST_TEST:/tmp/
    - ssh $SSH_USER_TEST@$SSH_HOST_TEST "chmod +x /tmp/restart_test.sh"
    - ssh $SSH_USER_TEST@$SSH_HOST_TEST "/tmp/restart_test.sh"
  environment:
    name: test
  only:
    - develop