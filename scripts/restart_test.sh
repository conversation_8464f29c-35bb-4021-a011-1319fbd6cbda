#!/bin/bash

# FastAPI应用平滑重启脚本 - 测试环境版本
# 使用方法: ./restart_test.sh [container_name] [image_name]

set -e

CONTAINER_NAME=${1:-"ai_server_test"}
IMAGE_NAME=${2:-"************:9092/ai_server:test"}
HEALTH_CHECK_URL="http://localhost:30102/health"
MAX_WAIT_TIME=60

echo "🚀 开始平滑重启 $CONTAINER_NAME (使用Gunicorn优雅重启)..."

echo "🧪 测试环境部署 - 使用Docker镜像内置配置"

# 检查容器是否存在并运行
if ! docker ps --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
    echo "📦 容器 $CONTAINER_NAME 不存在或未运行，直接启动新容器"

    # 检查端口是否被占用
    echo "🔍 检查端口30102是否被占用..."
    if netstat -tuln | grep :30102; then
        echo "⚠️  端口30102已被占用，尝试清理..."
        docker ps -a | grep ":30102->" | awk '{print $1}' | xargs -r docker stop
        docker ps -a | grep ":30102->" | awk '{print $1}' | xargs -r docker rm
    fi

    echo "🚀 启动新容器..."
    CONTAINER_ID=$(docker run -d --name $CONTAINER_NAME -p 30102:30101 \
        -v /data/server/logs_test:/app/logs \
        -e ENVIRONMENT=test \
        -e DEBUG=true \
        --restart unless-stopped \
        $IMAGE_NAME)

    echo "📋 新容器ID: $CONTAINER_ID"

    echo "⏳ 等待新容器启动..."
    sleep 10

    # 检查容器状态
    echo "🔍 检查容器状态..."
    docker ps -f name=$CONTAINER_NAME

    # 如果容器没有运行，显示日志
    if ! docker ps -f name=$CONTAINER_NAME | grep -q $CONTAINER_NAME; then
        echo "❌ 容器未运行，显示容器日志："
        docker logs $CONTAINER_NAME
        exit 1
    fi

    if curl -f -s $HEALTH_CHECK_URL > /dev/null; then
        echo "✅ 新容器启动成功！"
    else
        echo "❌ 健康检查失败，显示容器日志："
        docker logs --tail 50 $CONTAINER_NAME
        exit 1
    fi
    exit 0
fi

CONTAINER_ID=$(docker ps -q -f name=$CONTAINER_NAME)
echo "📋 当前容器ID: $CONTAINER_ID"

# 检查当前容器健康状态
echo "🔍 检查当前容器状态..."
if ! curl -f -s $HEALTH_CHECK_URL > /dev/null; then
    echo "⚠️  当前容器不健康，执行强制重启"
    echo "🛑 停止旧容器..."
    docker stop $CONTAINER_NAME
    echo "🗑️  删除旧容器..."
    docker rm $CONTAINER_NAME

    echo "🚀 启动新容器..."
    CONTAINER_ID=$(docker run -d --name $CONTAINER_NAME -p 30102:30101 \
        -v /data/server/logs_test:/app/logs \
        -e ENVIRONMENT=test \
        -e DEBUG=true \
        --restart unless-stopped \
        $IMAGE_NAME)

    echo "📋 新容器ID: $CONTAINER_ID"

    # 检查容器是否成功启动
    sleep 5
    if ! docker ps -f name=$CONTAINER_NAME | grep -q $CONTAINER_NAME; then
        echo "❌ 容器重启失败，显示日志："
        docker logs $CONTAINER_NAME
        exit 1
    fi

    exit 0
fi

# 使用Gunicorn的SIGHUP信号进行优雅重启
echo "🔄 发送SIGHUP信号给Gunicorn主进程进行优雅重启..."

# 尝试获取容器内Gunicorn主进程PID
GUNICORN_PID=""
if docker exec $CONTAINER_NAME which pgrep >/dev/null 2>&1; then
    # 方法1: 使用pgrep查找gunicorn主进程（父进程）
    GUNICORN_PID=$(docker exec $CONTAINER_NAME pgrep -f "gunicorn.*app.main:app" 2>/dev/null | head -1)

    # 方法2: 如果方法1失败，尝试查找最老的gunicorn进程（通常是主进程）
    if [ -z "$GUNICORN_PID" ]; then
        GUNICORN_PID=$(docker exec $CONTAINER_NAME pgrep -o -f "gunicorn" 2>/dev/null)
    fi
else
    echo "⚠️  容器中未找到pgrep命令，尝试其他方法..."
    # 使用ps命令作为备用方案 - 查找父进程ID最小的gunicorn进程
    GUNICORN_PID=$(docker exec $CONTAINER_NAME ps -eo pid,ppid,cmd 2>/dev/null | grep "gunicorn.*app.main:app" | grep -v grep | sort -k2 -n | head -1 | awk '{print $1}')
fi

# 验证找到的PID是否确实是主进程（检查是否有子进程）
if [ -n "$GUNICORN_PID" ]; then
    CHILD_COUNT=$(docker exec $CONTAINER_NAME ps --ppid $GUNICORN_PID 2>/dev/null | wc -l)
    if [ "$CHILD_COUNT" -le 1 ]; then
        echo "⚠️  找到的进程 $GUNICORN_PID 没有子进程，可能不是主进程，重新查找..."
        # 重新查找：找到有最多子进程的gunicorn进程
        GUNICORN_PID=$(docker exec $CONTAINER_NAME bash -c 'for pid in $(pgrep -f gunicorn); do echo "$(ps --ppid $pid | wc -l) $pid"; done | sort -nr | head -1 | awk "{print \$2}"' 2>/dev/null)
    fi
fi

if [ -z "$GUNICORN_PID" ]; then
    echo "❌ 未找到Gunicorn主进程，使用容器重启方式..."

    # 拉取最新镜像
    echo "📥 拉取最新镜像..."
    docker pull $IMAGE_NAME

    # 发送SIGTERM信号优雅关闭
    echo "🛑 发送优雅关闭信号..."
    docker kill --signal=SIGTERM $CONTAINER_ID

    # 等待容器优雅关闭
    echo "⏳ 等待容器优雅关闭..."
    timeout 30 docker wait $CONTAINER_NAME || {
        echo "⚠️  优雅关闭超时，强制停止容器"
        docker kill $CONTAINER_NAME
    }

    # 删除旧容器
    docker rm $CONTAINER_NAME

    # 启动新容器
    echo "🚀 启动新容器..."
    docker run -d --name $CONTAINER_NAME -p 30102:30101 \
        -v /data/server/logs_test:/app/logs \
        -e ENVIRONMENT=test \
        -e DEBUG=true \
        --restart unless-stopped \
        $IMAGE_NAME
else
    echo "📍 找到Gunicorn主进程PID: $GUNICORN_PID"

    # 发送SIGHUP信号给Gunicorn主进程
    if docker exec $CONTAINER_NAME which kill >/dev/null 2>&1; then
        echo "📤 发送SIGHUP信号..."
        docker exec $CONTAINER_NAME kill -HUP $GUNICORN_PID
        echo "⏳ 等待Gunicorn重新加载worker进程..."
        sleep 10
    else
        echo "❌ 容器中未找到kill命令，回退到容器重启方式..."
        # 回退到容器重启
        docker kill --signal=SIGTERM $CONTAINER_ID
        timeout 30 docker wait $CONTAINER_NAME || docker kill $CONTAINER_NAME
        docker rm $CONTAINER_NAME
        docker run -d --name $CONTAINER_NAME -p 30102:30101 \
            -v /data/server/logs_test:/app/logs \
            -e ENVIRONMENT=test \
            -e DEBUG=true \
            --restart unless-stopped \
            $IMAGE_NAME
    fi
fi

# 等待服务恢复
echo "🔍 等待服务恢复..."
WAIT_TIME=0
while [ $WAIT_TIME -lt $MAX_WAIT_TIME ]; do
    if curl -f -s $HEALTH_CHECK_URL > /dev/null; then
        echo "✅ 服务恢复正常"
        break
    fi
    echo "⏳ 等待中... ($WAIT_TIME/$MAX_WAIT_TIME 秒)"
    sleep 2
    WAIT_TIME=$((WAIT_TIME + 2))
done

if [ $WAIT_TIME -ge $MAX_WAIT_TIME ]; then
    echo "❌ 服务恢复超时"
    exit 1
fi

echo "🎉 平滑重启完成！服务正常运行"

# 显示当前状态
echo "📊 当前服务状态:"
curl -s $HEALTH_CHECK_URL | python3 -m json.tool 2>/dev/null || echo "健康检查响应获取失败"
